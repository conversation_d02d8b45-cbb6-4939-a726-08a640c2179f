* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    height: 100vh;
}

#container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#musicBox {
    display: block;
    width: 100%;
    height: 100%;
}

#controls {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 300px;
    z-index: 100;
}

#controls h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.control-panel {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.play-button {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.play-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.play-button:active {
    transform: translateY(0);
}

.play-button.playing {
    background: linear-gradient(45deg, #26de81, #20bf6b);
    box-shadow: 0 4px 15px rgba(38, 222, 129, 0.4);
}

.volume-control, .song-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}

.volume-control label, .song-selector label {
    font-weight: bold;
    color: #333;
    min-width: 60px;
}

.volume-control input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ff6b6b;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.volume-control input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ff6b6b;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

#volumeValue {
    font-weight: bold;
    color: #666;
    min-width: 40px;
}

#songSelect {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

#songSelect:focus {
    border-color: #ff6b6b;
}

.info {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.info p {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

#loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 200;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loading p {
    font-size: 18px;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #controls {
        top: 10px;
        left: 10px;
        right: 10px;
        min-width: auto;
        padding: 15px;
    }
    
    #controls h1 {
        font-size: 20px;
    }
    
    .play-button {
        padding: 12px 20px;
        font-size: 14px;
    }
    
    .volume-control, .song-selector {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }
    
    .volume-control label, .song-selector label {
        min-width: auto;
    }
}

/* 隐藏加载动画的类 */
.hidden {
    display: none !important;
}
