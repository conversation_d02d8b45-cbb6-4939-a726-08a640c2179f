// 3D八音盒主要变量
let scene, camera, renderer, controls;
let musicBox, ballerina, musicBoxLid;
let isPlaying = false;
let currentSong = 0;
let audioContext, audioBuffer, audioSource;

// 音乐数据 - 使用Web Audio API生成音调
const songs = [
    { name: "天空之城", notes: [523, 587, 659, 698, 784, 880, 988, 1047] },
    { name: "卡农", notes: [392, 440, 494, 523, 587, 659, 740, 831] },
    { name: "月光奏鸣曲", notes: [262, 294, 330, 349, 392, 440, 494, 523] },
    { name: "小星星", notes: [523, 523, 784, 784, 880, 880, 784, 698, 698, 659, 659, 587, 587, 523] }
];

// 初始化函数
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x87CEEB);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(5, 5, 5);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ 
        canvas: document.getElementById('musicBox'),
        antialias: true 
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.maxDistance = 15;
    controls.minDistance = 3;

    // 添加光源
    addLights();
    
    // 创建八音盒
    createMusicBox();

    // 增强场景
    enhanceScene();

    // 初始化音频
    initAudio();

    // 添加事件监听器
    addEventListeners();

    // 隐藏加载动画
    document.getElementById('loading').classList.add('hidden');

    // 开始渲染循环
    animate();
}

// 添加光源
function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);

    // 点光源（暖色调）
    const pointLight = new THREE.PointLight(0xffaa88, 0.5, 20);
    pointLight.position.set(-5, 8, -5);
    scene.add(pointLight);
}

// 创建八音盒
function createMusicBox() {
    const boxGroup = new THREE.Group();
    
    // 八音盒底座
    const baseGeometry = new THREE.BoxGeometry(4, 0.5, 3);
    const baseMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x8B4513,
        transparent: true,
        opacity: 0.9
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = -0.25;
    base.castShadow = true;
    base.receiveShadow = true;
    boxGroup.add(base);

    // 八音盒主体
    const boxGeometry = new THREE.BoxGeometry(3.8, 2, 2.8);
    const boxMaterial = new THREE.MeshLambertMaterial({ 
        color: 0xDEB887,
        transparent: true,
        opacity: 0.8
    });
    musicBox = new THREE.Mesh(boxGeometry, boxMaterial);
    musicBox.position.y = 1;
    musicBox.castShadow = true;
    musicBox.receiveShadow = true;
    boxGroup.add(musicBox);

    // 八音盒盖子
    const lidGeometry = new THREE.BoxGeometry(4, 0.3, 3);
    const lidMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x654321,
        transparent: true,
        opacity: 0.9
    });
    musicBoxLid = new THREE.Mesh(lidGeometry, lidMaterial);
    musicBoxLid.position.set(0, 2.5, 0);
    musicBoxLid.castShadow = true;
    boxGroup.add(musicBoxLid);

    // 创建芭蕾舞者
    createBallerina(boxGroup);
    
    // 添加装饰
    addDecorations(boxGroup);
    
    scene.add(boxGroup);
}

// 创建芭蕾舞者
function createBallerina(parent) {
    const ballerinaGroup = new THREE.Group();
    
    // 身体
    const bodyGeometry = new THREE.CylinderGeometry(0.2, 0.3, 1.2, 8);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0xFFB6C1 });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.6;
    ballerinaGroup.add(body);

    // 头部
    const headGeometry = new THREE.SphereGeometry(0.25, 16, 16);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBDB });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.4;
    ballerinaGroup.add(head);

    // 裙子
    const skirtGeometry = new THREE.ConeGeometry(0.8, 0.6, 12);
    const skirtMaterial = new THREE.MeshLambertMaterial({ 
        color: 0xFF69B4,
        transparent: true,
        opacity: 0.8
    });
    const skirt = new THREE.Mesh(skirtGeometry, skirtMaterial);
    skirt.position.y = 0.1;
    ballerinaGroup.add(skirt);

    // 手臂
    const armGeometry = new THREE.CylinderGeometry(0.05, 0.08, 0.8, 6);
    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xFFDBDB });
    
    const leftArm = new THREE.Mesh(armGeometry, armMaterial);
    leftArm.position.set(-0.4, 0.8, 0);
    leftArm.rotation.z = Math.PI / 4;
    ballerinaGroup.add(leftArm);
    
    const rightArm = new THREE.Mesh(armGeometry, armMaterial);
    rightArm.position.set(0.4, 0.8, 0);
    rightArm.rotation.z = -Math.PI / 4;
    ballerinaGroup.add(rightArm);

    ballerinaGroup.position.set(0, 2.2, 0);
    ballerina = ballerinaGroup;
    parent.add(ballerinaGroup);
}

// 添加装饰
function addDecorations(parent) {
    // 添加一些小装饰品
    for (let i = 0; i < 8; i++) {
        const gemGeometry = new THREE.SphereGeometry(0.1, 8, 8);
        const gemMaterial = new THREE.MeshLambertMaterial({ 
            color: new THREE.Color().setHSL(i / 8, 0.8, 0.6),
            transparent: true,
            opacity: 0.8
        });
        const gem = new THREE.Mesh(gemGeometry, gemMaterial);
        
        const angle = (i / 8) * Math.PI * 2;
        gem.position.set(
            Math.cos(angle) * 1.5,
            1.8,
            Math.sin(angle) * 1.2
        );
        parent.add(gem);
    }
}

// 初始化音频
function initAudio() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
        console.log('Web Audio API 不支持');
    }
}

// 播放音乐
function playMusic() {
    if (!audioContext) return;
    
    const song = songs[currentSong];
    let noteIndex = 0;
    
    function playNote() {
        if (!isPlaying) return;
        
        const frequency = song.notes[noteIndex % song.notes.length];
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.8);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.8);
        
        noteIndex++;
        
        if (isPlaying) {
            setTimeout(playNote, 600);
        }
    }
    
    playNote();
}

// 停止音乐
function stopMusic() {
    isPlaying = false;
}

// 添加事件监听器
function addEventListeners() {
    const playBtn = document.getElementById('playBtn');
    const playIcon = document.getElementById('playIcon');
    const playText = document.getElementById('playText');
    const volumeSlider = document.getElementById('volume');
    const volumeValue = document.getElementById('volumeValue');
    const songSelect = document.getElementById('songSelect');

    // 播放/暂停按钮
    playBtn.addEventListener('click', () => {
        if (isPlaying) {
            stopMusic();
            playBtn.classList.remove('playing');
            playIcon.textContent = '▶️';
            playText.textContent = '播放音乐';
        } else {
            isPlaying = true;
            playMusic();
            playBtn.classList.add('playing');
            playIcon.textContent = '⏸️';
            playText.textContent = '暂停音乐';
        }
    });

    // 音量控制
    volumeSlider.addEventListener('input', (e) => {
        const volume = e.target.value;
        volumeValue.textContent = volume + '%';
        if (audioContext) {
            audioContext.destination.gain = volume / 100;
        }
    });

    // 歌曲选择
    songSelect.addEventListener('change', (e) => {
        currentSong = parseInt(e.target.value);
        if (isPlaying) {
            stopMusic();
            setTimeout(() => {
                isPlaying = true;
                playMusic();
            }, 100);
        }
    });

    // 窗口大小调整
    window.addEventListener('resize', onWindowResize);
}

// 窗口大小调整
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    // 更新控制器
    controls.update();

    // 如果音乐在播放，旋转芭蕾舞者和盒盖
    if (isPlaying && ballerina) {
        ballerina.rotation.y += 0.02;

        // 盒盖轻微摆动
        if (musicBoxLid) {
            musicBoxLid.rotation.z = Math.sin(Date.now() * 0.003) * 0.05;
        }

        // 八音盒轻微震动
        if (musicBox) {
            musicBox.position.y = 1 + Math.sin(Date.now() * 0.01) * 0.02;
        }
    }

    // 渲染场景
    renderer.render(scene, camera);
}

// 页面加载完成后初始化
window.addEventListener('load', () => {
    // 延迟一点时间让页面完全加载
    setTimeout(init, 500);
});

// 添加一些粒子效果
function createParticles() {
    const particleCount = 100;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);

    for (let i = 0; i < particleCount * 3; i += 3) {
        positions[i] = (Math.random() - 0.5) * 20;     // x
        positions[i + 1] = Math.random() * 10;         // y
        positions[i + 2] = (Math.random() - 0.5) * 20; // z
    }

    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const particleMaterial = new THREE.PointsMaterial({
        color: 0xffffff,
        size: 0.1,
        transparent: true,
        opacity: 0.6
    });

    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);

    return particleSystem;
}

// 添加地面
function addGround() {
    const groundGeometry = new THREE.PlaneGeometry(20, 20);
    const groundMaterial = new THREE.MeshLambertMaterial({
        color: 0x90EE90,
        transparent: true,
        opacity: 0.3
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -1;
    ground.receiveShadow = true;
    scene.add(ground);
}

// 在init函数中添加粒子和地面
function enhanceScene() {
    createParticles();
    addGround();
}

// 修改init函数，在创建八音盒后添加场景增强
// 这个函数会在原有的init函数中被调用
