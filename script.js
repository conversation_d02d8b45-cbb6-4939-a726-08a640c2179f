// 3D钢琴主要变量
let scene, camera, renderer, controls;
let piano, pianoKeys = [];
let isAutoPlaying = false;
let currentSong = 0;
let playMode = 'auto';
let audioContext;

// 钢琴键盘映射
const keyboardMapping = {
    'a': 0, 's': 1, 'd': 2, 'f': 3, 'g': 4, 'h': 5, 'j': 6, 'k': 7, 'l': 8,
    'w': 0.5, 'e': 1.5, 't': 3.5, 'y': 4.5, 'u': 5.5, 'o': 7.5, 'p': 8.5
};

// 钢琴音符频率 (C4-C5)
const noteFrequencies = [
    261.63, 277.18, 293.66, 311.13, 329.63, 349.23, 369.99, 392.00, 415.30, 440.00, 466.16, 493.88, 523.25
];

// 钢琴曲目数据
const pianoSongs = [
    {
        name: "月光奏鸣曲",
        notes: [
            {note: 4, time: 0, duration: 1000},
            {note: 7, time: 500, duration: 1000},
            {note: 11, time: 1000, duration: 1000},
            {note: 7, time: 1500, duration: 500},
            {note: 4, time: 2000, duration: 1000}
        ]
    },
    {
        name: "致爱丽丝",
        notes: [
            {note: 8, time: 0, duration: 400},
            {note: 7, time: 400, duration: 400},
            {note: 8, time: 800, duration: 400},
            {note: 7, time: 1200, duration: 400},
            {note: 8, time: 1600, duration: 400},
            {note: 3, time: 2000, duration: 400},
            {note: 6, time: 2400, duration: 400},
            {note: 4, time: 2800, duration: 800}
        ]
    },
    {
        name: "卡农",
        notes: [
            {note: 0, time: 0, duration: 1000},
            {note: 4, time: 1000, duration: 1000},
            {note: 7, time: 2000, duration: 1000},
            {note: 4, time: 3000, duration: 1000}
        ]
    },
    {
        name: "小星星变奏曲",
        notes: [
            {note: 0, time: 0, duration: 500},
            {note: 0, time: 500, duration: 500},
            {note: 7, time: 1000, duration: 500},
            {note: 7, time: 1500, duration: 500},
            {note: 9, time: 2000, duration: 500},
            {note: 9, time: 2500, duration: 500},
            {note: 7, time: 3000, duration: 1000}
        ]
    }
];

// 初始化函数
function init() {
    // 创建场景
    scene = new THREE.Scene();
    scene.background = new THREE.Color(0x2c3e50);

    // 创建相机
    camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 8, 12);

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ 
        canvas: document.getElementById('piano3D'),
        antialias: true 
    });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 创建控制器
    controls = new THREE.OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.maxDistance = 25;
    controls.minDistance = 5;
    controls.maxPolarAngle = Math.PI / 2.2;

    // 添加光源
    addLights();
    
    // 创建钢琴
    createPiano();
    
    // 初始化音频
    initAudio();
    
    // 添加事件监听器
    addEventListeners();
    
    // 隐藏加载动画
    document.getElementById('loading').classList.add('hidden');
    
    // 开始渲染循环
    animate();
}

// 添加光源
function addLights() {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    scene.add(ambientLight);

    // 主光源
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 15, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;
    scene.add(directionalLight);

    // 聚光灯（照亮钢琴）
    const spotLight = new THREE.SpotLight(0xffffff, 0.6);
    spotLight.position.set(0, 20, 0);
    spotLight.target.position.set(0, 0, 0);
    spotLight.angle = Math.PI / 4;
    spotLight.penumbra = 0.3;
    spotLight.castShadow = true;
    scene.add(spotLight);
    scene.add(spotLight.target);
}

// 创建钢琴
function createPiano() {
    const pianoGroup = new THREE.Group();
    
    // 钢琴主体
    const pianoBodyGeometry = new THREE.BoxGeometry(12, 1, 4);
    const pianoBodyMaterial = new THREE.MeshLambertMaterial({ color: 0x1a1a1a });
    const pianoBody = new THREE.Mesh(pianoBodyGeometry, pianoBodyMaterial);
    pianoBody.position.y = 0.5;
    pianoBody.castShadow = true;
    pianoBody.receiveShadow = true;
    pianoGroup.add(pianoBody);

    // 钢琴腿
    createPianoLegs(pianoGroup);
    
    // 创建琴键
    createPianoKeys(pianoGroup);
    
    // 钢琴盖
    const lidGeometry = new THREE.BoxGeometry(12, 0.2, 4);
    const lidMaterial = new THREE.MeshLambertMaterial({ color: 0x0f0f0f });
    const lid = new THREE.Mesh(lidGeometry, lidMaterial);
    lid.position.set(0, 1.6, 0);
    lid.castShadow = true;
    pianoGroup.add(lid);

    piano = pianoGroup;
    scene.add(pianoGroup);
}

// 创建钢琴腿
function createPianoLegs(parent) {
    const legGeometry = new THREE.CylinderGeometry(0.1, 0.15, 3, 8);
    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x1a1a1a });
    
    const legPositions = [
        [-5, -1.5, -1.5],
        [5, -1.5, -1.5],
        [-5, -1.5, 1.5],
        [5, -1.5, 1.5]
    ];
    
    legPositions.forEach(pos => {
        const leg = new THREE.Mesh(legGeometry, legMaterial);
        leg.position.set(pos[0], pos[1], pos[2]);
        leg.castShadow = true;
        parent.add(leg);
    });
}

// 创建琴键
function createPianoKeys(parent) {
    const whiteKeyGeometry = new THREE.BoxGeometry(0.8, 0.3, 3);
    const blackKeyGeometry = new THREE.BoxGeometry(0.5, 0.4, 1.8);
    
    const whiteKeyMaterial = new THREE.MeshLambertMaterial({ color: 0xffffff });
    const blackKeyMaterial = new THREE.MeshLambertMaterial({ color: 0x1a1a1a });
    
    // 白键
    for (let i = 0; i < 13; i++) {
        const whiteKey = new THREE.Mesh(whiteKeyGeometry, whiteKeyMaterial);
        whiteKey.position.set((i - 6) * 0.9, 1.15, 0.5);
        whiteKey.castShadow = true;
        whiteKey.receiveShadow = true;
        whiteKey.userData = { keyIndex: i, isBlack: false, originalY: 1.15 };
        pianoKeys.push(whiteKey);
        parent.add(whiteKey);
    }
    
    // 黑键
    const blackKeyPositions = [0.5, 1.5, 3.5, 4.5, 5.5, 7.5, 8.5];
    blackKeyPositions.forEach((pos) => {
        const blackKey = new THREE.Mesh(blackKeyGeometry, blackKeyMaterial);
        blackKey.position.set((pos - 6) * 0.9, 1.35, 1.4);
        blackKey.castShadow = true;
        blackKey.userData = { keyIndex: pos, isBlack: true, originalY: 1.35 };
        pianoKeys.push(blackKey);
        parent.add(blackKey);
    });
}

// 初始化音频
function initAudio() {
    try {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    } catch (e) {
        console.log('Web Audio API 不支持');
    }
}

// 播放音符
function playNote(keyIndex, duration = 1000) {
    if (!audioContext) return;
    
    const frequency = noteFrequencies[Math.floor(keyIndex)];
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.type = 'sine';
    
    // 音量包络
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration / 1000);
    
    // 视觉反馈
    animateKey(keyIndex);
}

// 琴键动画
function animateKey(keyIndex) {
    const key = pianoKeys.find(k => Math.floor(k.userData.keyIndex) === Math.floor(keyIndex));
    if (key) {
        const originalY = key.userData.originalY;
        key.position.y = originalY - 0.1;

        setTimeout(() => {
            key.position.y = originalY;
        }, 200);
    }
}

// 自动演奏
function autoPlay() {
    if (!isAutoPlaying) return;

    const song = pianoSongs[currentSong];

    song.notes.forEach(noteData => {
        setTimeout(() => {
            if (isAutoPlaying) {
                playNote(noteData.note, noteData.duration);
            }
        }, noteData.time);
    });

    // 循环播放
    const songDuration = Math.max(...song.notes.map(n => n.time + n.duration));
    setTimeout(() => {
        if (isAutoPlaying) {
            autoPlay();
        }
    }, songDuration + 1000);
}

// 停止自动演奏
function stopAutoPlay() {
    isAutoPlaying = false;
}

// 鼠标点击琴键
function onPianoKeyClick(event) {
    if (playMode !== 'manual') return;

    const mouse = new THREE.Vector2();
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    const raycaster = new THREE.Raycaster();
    raycaster.setFromCamera(mouse, camera);

    const intersects = raycaster.intersectObjects(pianoKeys);

    if (intersects.length > 0) {
        const clickedKey = intersects[0].object;
        playNote(clickedKey.userData.keyIndex);
    }
}

// 键盘按键
function onKeyDown(event) {
    if (playMode !== 'manual') return;

    const key = event.key.toLowerCase();
    if (keyboardMapping.hasOwnProperty(key)) {
        playNote(keyboardMapping[key]);
    }
}

// 添加事件监听器
function addEventListeners() {
    const playBtn = document.getElementById('playBtn');
    const playIcon = document.getElementById('playIcon');
    const playText = document.getElementById('playText');
    const volumeSlider = document.getElementById('volume');
    const volumeValue = document.getElementById('volumeValue');
    const songSelect = document.getElementById('songSelect');
    const modeSelect = document.getElementById('modeSelect');

    // 播放/暂停按钮
    playBtn.addEventListener('click', () => {
        if (playMode === 'auto') {
            if (isAutoPlaying) {
                stopAutoPlay();
                playBtn.classList.remove('playing');
                playIcon.textContent = '▶️';
                playText.textContent = '自动演奏';
            } else {
                isAutoPlaying = true;
                autoPlay();
                playBtn.classList.add('playing');
                playIcon.textContent = '⏸️';
                playText.textContent = '停止演奏';
            }
        }
    });

    // 演奏模式切换
    modeSelect.addEventListener('change', (e) => {
        playMode = e.target.value;
        if (playMode === 'manual') {
            stopAutoPlay();
            playBtn.classList.remove('playing');
            playIcon.textContent = '🎹';
            playText.textContent = '手动模式';
            playBtn.disabled = true;
        } else {
            playBtn.disabled = false;
            playIcon.textContent = '▶️';
            playText.textContent = '自动演奏';
        }
    });

    // 音量控制
    volumeSlider.addEventListener('input', (e) => {
        const volume = e.target.value;
        volumeValue.textContent = volume + '%';
        if (audioContext && audioContext.destination.gain) {
            audioContext.destination.gain.value = volume / 100;
        }
    });

    // 歌曲选择
    songSelect.addEventListener('change', (e) => {
        currentSong = parseInt(e.target.value);
        if (isAutoPlaying) {
            stopAutoPlay();
            setTimeout(() => {
                isAutoPlaying = true;
                autoPlay();
            }, 100);
        }
    });

    // 鼠标点击琴键
    renderer.domElement.addEventListener('click', onPianoKeyClick);

    // 键盘按键
    document.addEventListener('keydown', onKeyDown);

    // 窗口大小调整
    window.addEventListener('resize', onWindowResize);
}

// 窗口大小调整
function onWindowResize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// 动画循环
function animate() {
    requestAnimationFrame(animate);

    // 更新控制器
    controls.update();

    // 渲染场景
    renderer.render(scene, camera);
}

// 添加地面
function addGround() {
    const groundGeometry = new THREE.PlaneGeometry(30, 30);
    const groundMaterial = new THREE.MeshLambertMaterial({
        color: 0x34495e,
        transparent: true,
        opacity: 0.8
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.position.y = -3;
    ground.receiveShadow = true;
    scene.add(ground);
}

// 页面加载完成后初始化
window.addEventListener('load', () => {
    setTimeout(() => {
        init();
        addGround();
    }, 500);
});
