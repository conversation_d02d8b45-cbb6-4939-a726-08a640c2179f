<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D钢琴</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
</head>
<body>
    <div id="container">
        <canvas id="piano3D"></canvas>

        <div id="controls">
            <h1>� 3D钢琴 �</h1>

            <div class="control-panel">
                <button id="playBtn" class="play-button">
                    <span id="playIcon">▶️</span>
                    <span id="playText">自动演奏</span>
                </button>

                <div class="mode-selector">
                    <label for="modeSelect">演奏模式:</label>
                    <select id="modeSelect">
                        <option value="auto">自动演奏</option>
                        <option value="manual">手动弹奏</option>
                    </select>
                </div>

                <div class="volume-control">
                    <label for="volume">音量:</label>
                    <input type="range" id="volume" min="0" max="100" value="70">
                    <span id="volumeValue">70%</span>
                </div>

                <div class="song-selector">
                    <label for="songSelect">选择曲目:</label>
                    <select id="songSelect">
                        <option value="0">月光奏鸣曲</option>
                        <option value="1">致爱丽丝</option>
                        <option value="2">卡农</option>
                        <option value="3">小星星变奏曲</option>
                    </select>
                </div>
            </div>

            <div class="info">
                <p>� 点击琴键或使用键盘弹奏</p>
                <p>🖱️ 拖拽鼠标旋转视角</p>
                <p>🔍 滚轮缩放</p>
                <p>⌨️ 键盘映射: A-L 对应白键</p>
            </div>
        </div>

        <div id="loading">
            <div class="spinner"></div>
            <p>正在加载3D钢琴...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>